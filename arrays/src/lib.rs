pub fn sum(a: &[i32]) -> i32 {
    a.iter().sum()
  }
  
  pub fn thirtytwo_tens() -> [i32; 32] {
      [10; 32]
  }
  
  
  #[cfg(test)]
  mod tests {
      use super::*;
  
      #[test]
      fn it_works() {
          let a = (1..=10).collect::<Vec<i32>>().to_vec();
          let b = [5; 10].to_vec();
  
          let result1 = sum(&a);
          let result2 = sum(&b);
          let result3 = thirtytwo_tens();
          assert_eq!(result1, 55);
          assert_eq!(result2, 50);
          assert_eq!(result3, [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10]);
      }
  }