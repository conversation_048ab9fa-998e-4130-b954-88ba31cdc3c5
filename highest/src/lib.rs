#[derive(Debug)]
pub struct Numbers<'a> {
    numbers: &'a [u32],
}

impl <'a> Numbers <'a> {
    pub fn new(numbers: &'a[u32]) -> Self {
        Self{numbers}
    }

    pub fn list(&self) -> &[u32] {
        self.numbers
    }

    pub fn latest(&self) -> Option<u32> {
        self.numbers.last().copied()
    }

    pub fn highest(&self) -> Option<u32> {
        self.numbers.iter().max().copied()
    }

    pub fn highest_three(&self) -> Vec<u32> {
        let mut result: Vec<u32> = self.numbers.to_vec();
        result.sort_by(|a, b| b.cmp(a));
        result.truncate(3); 
        result
    }
}