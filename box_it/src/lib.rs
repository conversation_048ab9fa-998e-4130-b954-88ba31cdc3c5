pub fn transform_and_save_on_heap(s: String) -> Box<Vec<u32>> {
    let mut result = Vec::<u32>::new();
    let tokens = s.split_whitespace().collect::<Vec<&str>>();

    for token in tokens {
        if token.ends_with('k') {
            let val = &token[..token.len() - 1];
            let parsed = val.parse::<f32>().unwrap();
            result.push((parsed * 1000.0) as u32);
        } else {
            result.push(token.parse::<u32>().unwrap());
        }
    }

    Box::new(result)
}

pub fn take_value_ownership(a: Box<Vec<u32>>) -> Vec<u32> {
    *a
}