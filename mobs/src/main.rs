use mobs::*;

fn main() {
  let (mafia1, mafia2) = (
    Mob {
      name: "Hairy Giants".to_string(),
      boss: boss::Boss::new("<PERSON> Ha<PERSON>", 36),
      cities: vec![("San Francisco".to_string(), 7)],
      members: vec![
        member::Member::new("Benny Eggs", member::Role::<PERSON>, 28),
        member::Member::new("<PERSON><PERSON><PERSON>", member::Role::Associate, 17),
        member::Member::new("Greasy Thumb", member::Role::<PERSON>, 30),
        member::Member::new("No Finger", member::Role::<PERSON><PERSON><PERSON><PERSON>, 32),
      ],
      wealth: 100000,
    },
    Mob {
      name: "<PERSON> Thorns".to_string(),
      boss: boss::Boss::new("Big Tuna", 30),
      cities: vec![("San Jose".to_string(), 5)],
      members: vec![
        member::Member::new("Knuckle<PERSON>", member::Role::Soldier, 25),
        member::Member::new("Baldy Dom", member::Role::<PERSON><PERSON><PERSON><PERSON>, 36),
        member::Member::new("<PERSON> Joe", member::Role::Underbos<PERSON>, 23),
      ],
      wealth: 70000,
    },
  );

  println!("{:?}\n{:?}", mafia1, mafia2);
}