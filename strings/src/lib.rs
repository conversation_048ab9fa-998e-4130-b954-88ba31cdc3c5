pub fn char_length(s: &str) -> usize {
    s.chars().count()
}


#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works1() {
        let result = char_length("❤");
        assert_eq!(result, 1);
    }

    #[test]
    fn it_works2() {
        let result = char_length("形聲字");
        assert_eq!(result, 3);
    }

    #[test]
    fn it_works3() {
        let result = char_length("change");
        assert_eq!(result, 6);
    }
    #[test]
    fn it_works4() {
        let result = char_length("😍");
        assert_eq!(result, 1);
    }
}