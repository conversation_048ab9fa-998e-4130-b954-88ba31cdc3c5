mod messenger;

use std::collections::HashMap;
pub use std::cell::RefCell;
pub use std::rc::Rc;
pub use messenger::messenger::*;

pub struct Worker {
    pub track_value: Rc<usize>,
    pub mapped_messages: RefCell<HashMap<&'static str, String>>,
    pub all_messages: RefCell<Vec<String>>,
}

impl Worker {
    pub fn new(track_value: usize) -> Worker {
        Worker {
            track_value: Rc::new(track_value),
            mapped_messages: RefCell::new(HashMap::new()),
            all_messages: RefCell::new(Vec::new()),
        }
    }
}

impl Logger for Worker {
    fn warning(&self, msg: &str) {
        let mut mapped = self.mapped_messages.borrow_mut();
        mapped.insert("Warning", msg.replace("Warning: ", "").to_string());
        let mut all = self.all_messages.borrow_mut();
        all.push(msg.to_string())
    }
    fn info(&self, msg: &str) {
        let mut mapped = self.mapped_messages.borrow_mut();
        mapped.insert("Info", msg.replace("Info: ", "").to_string());
        let mut all = self.all_messages.borrow_mut();
        all.push(msg.to_string());
    }
    fn error(&self, msg: &str) {
        let mut mapped = self.mapped_messages.borrow_mut();
        mapped.insert("Error", msg.replace("Error: ", "").to_string());
        let mut all = self.all_messages.borrow_mut();
        all.push(msg.to_string());
    }
}



#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        let result = add(2, 2);
        assert_eq!(result, 4);
    }
}