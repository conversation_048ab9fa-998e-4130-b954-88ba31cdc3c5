pub mod messenger {
    use std::{cell::RefCell, rc::Rc};
    pub trait Logger {
        fn warning(&self, msg: &str);
        fn info(&self, msg: &str);
        fn error(&self, msg: &str);
    }

    pub struct Tracker<'a, T: Logger + 'a> {
        logger: &'a T,
        max: usize,
        value: RefCell<usize>
    }


    impl<'a, T: Logger> Tracker<'a, T> {
        pub fn new(logger: &'a T, max: usize) -> Tracker<'a, T> {
            Tracker {
                logger,
                max,
                value: RefCell::new(0),
            }
        }

        pub fn set_value(&self, value: &Rc<usize>) {
            let reference_value = Rc::strong_count(value);
            let mut borrowed_value = self.value.borrow_mut();
            *borrowed_value = reference_value;
            let percentage = (*borrowed_value as f64 / self.max as f64) * 100.0;

            if percentage >= 100.0 {
                self.logger.error("Error: you are over your quota!");
            } else if percentage >= 70.0 {
                self.logger
                    .warning(&format!("Warning: you have used up over {}% of your quota! Proceeds with precaution", percentage.floor()));
            }
        }

        pub fn peek(&self, tracked_value: &Rc<usize>) {
            let reference_value = Rc::strong_count(tracked_value);
            let percentage = (reference_value as f64 / self.max as f64) * 100.0;
            self.logger.info(&format!("Info: you are using up to {}% of your quota", percentage.floor()))
        }
    }
}