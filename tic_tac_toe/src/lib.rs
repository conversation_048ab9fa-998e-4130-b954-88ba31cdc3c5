pub fn tic_tac_toe(table: [[char; 3]; 3]) -> String {
    let player_x = 'X';
    let player_o = 'O';

    if diagonals(player_x, table) ||  horizontal(player_x, table) || vertical(player_x, table) {
        return format!("player {} won", player_x);
    } else if diagonals(player_o, table) || horizontal(player_o, table) || vertical(player_o, table) {
        return format!("player {} won", player_o);
    } else {
        "tie".to_string()
    }
}

pub fn diagonals(player: char, table: [[char; 3]; 3]) -> bool {
    if table[0][2] == player && table[1][1] == player && table[2][0] == player {
        return true;
    } else if table[0][0] == player && table[1][1] == player && table[2][2] == player {
        return true;
    } else {
        return false;
    }
}

pub fn horizontal(player: char, table: [[char; 3]; 3]) -> bool {
    if table[0][0] == player && table[0][1] == player && table[0][2] == player {
        return true;
    } else if table[1][0] == player && table[1][1] == player && table[1][2] == player {
        return true;
    } else if table[2][0] == player && table[2][1] == player && table[2][2] == player {
        return true;
    } else {
        return false;
    }
}

pub fn vertical(player: char, table: [[char; 3]; 3]) -> bool {
    if table[0][0] == player && table[1][0] == player && table[2][0] == player {
        return true;
    } else if table[0][1] == player && table[1][1] == player && table[2][1] == player {
        return true;
    } else if table[0][2] == player && table[1][2] == player && table[2][2] == player {
        return true;
    } else {
        return false;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn tic_tac_toe_works1() {
        let table = [['O', 'X', 'O'], ['O', 'P', 'X'], ['X', '#', 'X']];
        let result = tic_tac_toe(table);
        assert_eq!(result, String::from("tie"));
    }

    #[test]
    fn tic_tac_toe_works2() {
        let table = [['X', 'O', 'O'], ['X', 'O', 'O'], ['#', 'O', 'X']];
        let result = tic_tac_toe(table);
        assert_eq!(result, String::from("player O won"));
    }

    #[test]
    fn tic_tac_toe_works3() {
        let table = [['O', 'O', 'X'], ['O', 'X', 'O'], ['X', '#', 'X']];
        let result = tic_tac_toe(table);
        assert_eq!(result, String::from("player X won"));
    }
}