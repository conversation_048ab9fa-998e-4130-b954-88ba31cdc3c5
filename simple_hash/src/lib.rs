use std::collections::HashMap;

pub const SENTENCE: &str = "this is a very basic sentence with only a few repetitions. once again this is very basic but it should be enough for basic tests";

pub fn word_frequency_counter<'a>(words: &'a [&'a str]) -> HashMap<&'a str, usize> {
    let mut word_count = HashMap::new();

    for &word in words {
        *word_count.entry(word).or_insert(0) += 1;
    }

    word_count
}

pub fn nb_distinct_words(frequency_count: &HashMap<&str, usize>) -> usize {
    frequency_count.len()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {

        let words_slice = SENTENCE.split_ascii_whitespace().collect::<Vec<_>>();
        let frequency_count = word_frequency_counter(&words_slice);
        let frequency_of_uniques = nb_distinct_words(&frequency_count);
        let mut map: HashMap<&str, usize> = HashMap::new();

    map.insert("tests", 1);
    map.insert("with", 1);
    map.insert("this", 2);
    map.insert("it", 1);
    map.insert("enough", 1);
    map.insert("is", 2);
    map.insert("but", 1);
    map.insert("sentence", 1);
    map.insert("only", 1);
    map.insert("basic", 3);
    map.insert("again", 1);
    map.insert("for", 1);
    map.insert("be", 1);
    map.insert("once", 1);
    map.insert("very", 2);
    map.insert("should", 1);
    map.insert("few", 1);
    map.insert("a", 2);
    map.insert("repetitions.", 1);
        assert_eq!(frequency_count, map);
        assert_eq!(frequency_of_uniques, 19 as usize);
    }
}