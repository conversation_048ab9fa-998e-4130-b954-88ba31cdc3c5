pub fn pig_latin(text: &str) -> String {
    let vowels = ['a', 'e', 'i', 'o', 'u'];
    let chars = text.chars().collect::<Vec<char>>();
    if chars.is_empty() {
        return String::new();
    }

    // Check if word starts with vowel
    if vowels.contains(&chars[0].to_ascii_lowercase()) {
        return format!("{}ay", text);
    }

    // Special case: starts with 'squ' (move all 3 letters)
    if chars.len() >= 3 && chars[0] == 's' && chars[1] == 'q' && chars[2] == 'u' {
        let cluster = chars[0..3].iter().collect::<String>();
        let remaining = chars[3..].iter().collect::<String>();
        return format!("{}{}ay", remaining, cluster);
    }

    // Special case: consonant + 'qu' (move just the consonant)
    if chars.len() >= 2 && chars[1] == 'u' {
        let consonant = chars[0].to_string();
        let remaining = chars[1..].iter().collect::<String>();
        return format!("{}{}ay", remaining, consonant);
    }

    // Regular consonant case
    let mut first_vowel_pos = 0;
    for (i, &c) in chars.iter().enumerate() {
        if vowels.contains(&c.to_ascii_lowercase()) {
            first_vowel_pos = i;
            break;
        }
    }

    let consonants = chars[..first_vowel_pos].iter().collect::<String>();
    let remaining = chars[first_vowel_pos..].iter().collect::<String>();
    format!("{}{}ay", remaining, consonants)
}