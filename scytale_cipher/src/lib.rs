pub fn scytale_cipher(message: String, wraps: u32) -> String {
    let chars: Vec<char> = message.chars().collect();
    let len = chars.len();
    let wraps = wraps as usize;
    let rows = (len + wraps - 1) / wraps;

    let mut result = String::new();

    for i in 0..wraps {
        for j in 0..rows {
            let idx = j * wraps + i;
            if idx < len {
                result.push(chars[idx]);
            } else {
                result.push(' ');
            }
        }
    }

    result.trim_end().to_string()
}