#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>q, <PERSON><PERSON>)]
pub struct Light {
    pub alias: String,
    pub brightness: u8,
}

impl Light {
    pub fn new(alias: &str) -> Self {
        Self {
            alias: alias.to_string(),
            brightness: 0,
        }
    }
}

pub fn change_brightness(lights: &mut [Light], alias: &str, value: u8) {
    for light in lights {
        if light.alias == alias {
            light.brightness = value;
            break;
        }
    }
}