pub fn spell(n: u64) -> String {
    if n == 0 {
        return "zero".to_string();
    }

    if n == 1_000_000 {
        return "one million".to_string();
    }

    fn spell_chunk(n: u64) -> String {
        let mut parts = Vec::new();

        if n >= 100 {
            parts.push(format!("{} hundred", UNITS[(n / 100) as usize]));
        }

        let rem = n % 100;

        if rem >= 10 && rem < 20 {
            parts.push(TEENS[(rem - 10) as usize].to_string());
        } else {
            if rem >= 20 {
                parts.push(TENS[(rem / 10) as usize].to_string());
            }

            if rem % 10 > 0 {
                let word = UNITS[(rem % 10) as usize];
                if rem >= 20 {
                    // hyphen for compound numbers
                    let last = parts.pop().unwrap();
                    parts.push(format!("{}-{}", last, word));
                } else {
                    parts.push(word.to_string());
                }
            }
        }

        parts.join(" ")
    }

    const UNITS: [&str; 10] = [
        "", "one", "two", "three", "four", "five",
        "six", "seven", "eight", "nine",
    ];

    const TEENS: [&str; 10] = [
        "ten", "eleven", "twelve", "thirteen", "fourteen",
        "fifteen", "sixteen", "seventeen", "eighteen", "nineteen",
    ];

    const TENS: [&str; 10] = [
        "", "", "twenty", "thirty", "forty", "fifty",
        "sixty", "seventy", "eighty", "ninety",
    ];

    let mut result = String::new();

    if n >= 1_000 {
        let thousands = n / 1_000;
        result.push_str(&format!("{} thousand", spell_chunk(thousands)));
        let rem = n % 1_000;
        if rem > 0 {
            result.push(' ');
            result.push_str(&spell_chunk(rem));
        }
    } else {
        result.push_str(&spell_chunk(n));
    }

    result.trim().to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works1() {
        let result = spell(348);
        assert_eq!(result, "three hundred forty-eight".to_string());
    }

    #[test]
    fn it_works2() {
        let result = spell(9996);
        assert_eq!(result, "nine thousand nine hundred ninety-six".to_string());
    }
}