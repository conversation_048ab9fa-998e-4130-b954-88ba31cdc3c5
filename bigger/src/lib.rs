use std::collections::HashMap;

pub fn bigger(h: HashMap<&str, i32>) -> i32 {
    let mut max_value: i32 = 0;

    for (_, value) in h.iter() {
        if *value > max_value {
            max_value = *value;
        }
    }

    max_value
}


#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        let hash = HashMap::from_iter([
            ("<PERSON>", 122),
            ("<PERSON>", 333),
            ("<PERSON>", 334),
            ("<PERSON>", 14),
        ]);

        let result = bigger(hash);
        assert_eq!(result, 334);
    }
}