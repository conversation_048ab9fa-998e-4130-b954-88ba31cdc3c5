pub fn initials(names: Vec<&str>) -> Vec<String> {
    names
        .into_iter()
        .map(|name| {
            let mut initials = String::with_capacity(4); // Pre-allocate space for "X. Y."
            let mut words = name.split_whitespace();
            
            if let Some(first) = words.next() {
                initials.push(first.chars().next().unwrap_or(' '));
                initials.push('.');
            }
            
            if let Some(second) = words.next() {
                initials.push(' ');
                initials.push(second.chars().next().unwrap_or(' '));
                initials.push('.');
            }
            
            initials
        })
        .collect()
}

