pub fn first_subword(mut s: String) -> String {
    // Find the first separator (either an underscore or a lowercase followed by uppercase)
    let split_pos = s.char_indices()
        .skip(1) // Skip the first character to handle PascalCase
        .find(|(i, c)| c.is_uppercase() || *c == '_')
        .map(|(i, _)| i)
        .unwrap_or(s.len());
    
    // Split the string at the separator position
    s.truncate(split_pos);
    s
}