#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>q, PartialEq)]
pub struct GameSession {
    pub id: u32,
    pub p1: (String, u16),
    pub p2: (String, u16),
    pub nb_games: u16
}

impl GameSession {
    pub fn new(id: u32, p1_name: String, p2_name: String, nb_games: u16) -> Box<GameSession> {
        Box::new(GameSession {
            id,
            p1: (p1_name, 0),
            p2: (p2_name, 0),
            nb_games,
        })
    }

    pub fn read_winner(&self) -> (String, u16) {
        let (p1, score1) = &self.p1;
        let (p2, score2) = &self.p2;
        if score1 > score2 {
            (p1.clone(), *score1)
        } else if score1 < score2 {
            (p2.clone(), *score2)
        } else {
            ("Same score! tied".to_string(), *score1)
        }
    }

    pub fn update_score(&mut self, user_name: String) {
        let max_score = self.nb_games / 2 + 1;

          // check if game is already finished
          if self.p1.1 >= max_score || self.p2.1 >= max_score {
            return;
        }

        if self.p1.0 == user_name {
            self.p1.1 += 1;
        } else if self.p2.0 == user_name {
            self.p2.1 += 1;
        }
    }

    pub fn delete(self) -> String {
        format!("game deleted: id -> {}", self.id)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        let mut game = GameSession::new(0, String::from("Joao"), String::from("Susana"), 5);
        game.update_score(String::from("Joao"));
        game.update_score(String::from("Joao"));
        game.update_score(String::from("Susana"));
        game.update_score(String::from("Susana"));
        let result = game.read_winner();
        assert_eq!(result, ("Same score! tied".to_string(), 2));
    }
}