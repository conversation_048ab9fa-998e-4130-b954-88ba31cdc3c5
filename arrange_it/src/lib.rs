pub fn arrange_phrase(phrase: &str) -> String {
    // Split inputto individiual words
    let mut words: Vec<&str> = phrase.split_whitespace().collect();

    // Sort words based on digit contained in each word
    words.sort_by_key(|word| {
        word.chars()
        .find(|c| c.is_digit(10)).unwrap()
        .to_digit(10).unwrap()
    });

    // Filter out digit characters
    let mut clean_words: Vec<String> = words
    .iter()
    .map(|word| word.chars()
    .filter(|c| !c.is_digit(10))
    .collect())
    .collect();

    // Recontract words and return
    clean_words.join(" ")
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn arrange_phrase_works() {
        let subject = "is2 Thi1s T4est 3a";
        let result = arrange_phrase(subject);
        assert_eq!(result, "This is a Test");
    }
}