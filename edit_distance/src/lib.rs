pub fn edit_distance(source: &str, target: &str) -> usize {
    let m = source.len();
    let n = target.len();

    // Conver chr to vectors for easy insexing
    let source_chars: Vec<char> = source.chars().collect();
    let target_chars: Vec<char> = target.chars().collect();

    // Initialize a 2D matrix with zeros
    let mut dp = vec![vec![0; n+1]; m+1];

    // Populate the matrix
    for i in 0..=m {
        dp[i][0] = i;
    }
    for j in 0..=n {
        dp[0][j] = j;
    }

    // Fill in the rest
    for i in 1..=m {
        for j in 1..=n {
            let cost = if source_chars[i-1] == target_chars[j-1] { 0 } else { 1 };

            dp[i][j] = std::cmp::min(
                std::cmp::min(
                   
                        dp[i-1][j] + 1,
                        dp[i][j-1] + 1                    
                    ),
                    dp [i-1][j-1] + cost
            );
        }
    }
    dp[m][n]
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        let source = "alignment";
        let target = "assignment";
        let result = edit_distance(source, target);
        assert_eq!(result, 2);
    }
}