pub fn capitalize_first(input: &str) -> String {
    let mut chars = input.chars();
    match chars.next() {
        Some(first) if first.is_alphabetic() => first.to_uppercase().collect::<String>() + chars.as_str(),
        _ => input.to_string(),
    }
}

pub fn title_case(input: &str) -> String {
    input
    .split_inclusive(|c: char| c.is_whitespace())
    .map(|word| {
        let mut chars = word.chars();
        match chars.next() {
            Some(first) => first.to_uppercase().collect::<String>() + chars.as_str(),
            None => String::new(),
        }
    })
    .collect()
}

pub fn change_case(input: &str) -> String {
    input.chars()
    .map(|cha| 
    { if cha.is_uppercase(){
        cha.to_lowercase().collect::<String>()
    } else if cha.is_lowercase() {
        cha.to_uppercase().collect::<String>()
    } else {
        cha.to_string()
    }
    })
    .collect::<String>()
}
